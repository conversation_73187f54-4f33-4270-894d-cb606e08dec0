{"test_type": "torqueConst", "test_name": "关节扭矩常数测试", "description": "测试关节的扭矩常数特性，包括正向和反向扭矩特性以及重复性验证", "test_parameters": {"dac_max_voltage": 5.0, "motor_max_current": 5.0, "current_step_size": 10.0, "rated_torque": 10.0, "stability_tolerance": 0.05, "stability_duration": 5, "max_test_cycles": 3}, "test_steps": {"step1": "极限负载初始化 - 基于MODBUS-RTU协议与程控电源建立通信，设置磁粉制动器为最大电流输出", "step2": "正向扭矩特性测试 - 发送力位混控指令，以10%电流为步进单位增长，记录电流-扭矩数据，计算线性拟合斜率", "step3": "反向扭矩特性测试 - 反方向给予前馈电流，重复正向测试，生成反向特性曲线", "step4": "重复性验证 - 执行3次完整的正反向测试循环，计算重复性指标，评估一致性"}, "expected_results": {"positive_slope_range": [1.8, 2.2], "negative_slope_range": [-2.2, -1.8], "correlation_threshold": 0.9, "variation_coefficient_threshold": 0.1, "consistency_score_threshold": 0.8}, "data_recording": {"torque_sampling_rate": 10, "current_sampling_rate": 10, "position_sampling_rate": 10, "stability_check_samples": 10, "max_stability_wait_time": 30}, "safety_limits": {"max_current_limit": 6.0, "max_torque_limit": 12.0, "max_test_duration": 300, "emergency_stop_torque": 15.0}, "reporting": {"generate_plots": true, "save_raw_data": true, "export_format": ["json", "csv"], "include_statistics": true, "include_repeatability_analysis": true}, "hardware_interface": {"modbus_rtu": {"baud_rate": 9600, "data_bits": 8, "stop_bits": 1, "parity": "none", "timeout": 1000}, "torque_sensor": {"type": "strain_gauge", "range": "0-20 Nm", "accuracy": "0.1%", "sampling_rate": "100 Hz"}, "magnetic_powder_brake": {"type": "electromagnetic", "max_current": "5A", "control_method": "current_control", "response_time": "100ms"}}}