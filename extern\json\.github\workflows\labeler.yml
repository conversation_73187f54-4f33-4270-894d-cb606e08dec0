name: "Pull Request Labeler"

on:
  pull_request_target:
    types: [opened, synchronize]

permissions:
  contents: read

jobs:
  label:
    permissions:
      contents: read
      pull-requests: write

    runs-on: ubuntu-latest

    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: srvaroa/labeler@b4493338d7929ddc4ffc95fadf6f28c73bae2e90 # master
        env:
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
