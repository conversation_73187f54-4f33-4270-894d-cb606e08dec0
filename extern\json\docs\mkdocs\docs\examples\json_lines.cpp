#include <sstream>
#include <iostream>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main()
{
    // JSON Lines (see https://jsonlines.org)
    std::stringstream input;
    input << R"({"name": "<PERSON>", "wins": [["straight", "7♣"], ["one pair", "10♥"]]}
{"name": "<PERSON><PERSON>", "wins": [["two pair", "4♠"], ["two pair", "9♠"]]}
{"name": "May", "wins": []}
{"name": "<PERSON><PERSON>", "wins": [["three of a kind", "5♣"]]}
)";

    std::string line;
    while (std::getline(input, line))
    {
        std::cout << json::parse(line) << std::endl;
    }
}
