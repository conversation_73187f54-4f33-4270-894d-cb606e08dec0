{"files.associations": {"iostream": "cpp", "cmath": "cpp", "random": "cpp", "vector": "cpp", "chrono": "cpp", "numeric": "cpp", "thread": "cpp", "memory": "cpp", "cstring": "cpp", "mutex": "cpp", "map": "cpp", "functional": "cpp", "ostream": "cpp", "optional": "cpp", "system_error": "cpp", "array": "cpp", "atomic": "cpp", "cctype": "cpp", "clocale": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "unordered_map": "cpp", "exception": "cpp", "algorithm": "cpp", "iterator": "cpp", "memory_resource": "cpp", "ratio": "cpp", "string": "cpp", "string_view": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp", "iomanip": "cpp"}}