name: "Code scanning - action"

on:
  push:
    branches:
      - develop
      - master
      - release/*
  pull_request:
  schedule:
    - cron: '0 19 * * 1'
  workflow_dispatch:
  
concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  CodeQL-Build:

    runs-on: ubuntu-latest
    permissions:
      security-events: write

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
      with:
        egress-policy: audit

    - name: Checkout repository
      uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@3c3833e0f8c1c83d449a7478aa59c036a9165498 # v3.29.11
      with:
        languages: c-cpp

    # Autobuild attempts to build any compiled languages  (C/C++, C#, or Java).
    # If this step fails, then you should remove it and run the build manually (see below)
    - name: Autobuild
      uses: github/codeql-action/autobuild@3c3833e0f8c1c83d449a7478aa59c036a9165498 # v3.29.11

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@3c3833e0f8c1c83d449a7478aa59c036a9165498 # v3.29.11
