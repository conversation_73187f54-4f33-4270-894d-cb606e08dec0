# Warning flags determined for GCC 15.1.0 with https://github.com/nlohmann/gcc_flags:
# Ignored GCC warnings:
# -Wno-abi-tag           We do not care about ABI tags.
# -Wno-aggregate-return  The library uses aggregate returns.
# -Wno-long-long         The library uses the long long type to interface with system functions.
# -Wno-namespaces        The library uses namespaces.
# -Wno-nrvo              Doctest triggers this warning.
# -Wno-padded            We do not care about padding warnings.
# -Wno-system-headers    We do not care about warnings in system headers.
# -Wno-templates         The library uses templates.

set(GCC_CXXFLAGS
    -pedantic
    -Werror
    --all-warnings
    --extra-warnings
    -W
    -WNSObject-attribute
    -Wno-abi-tag
    -Waddress
    -Waddress-of-packed-member
    -Wno-aggregate-return
    -Waggressive-loop-optimizations
    -Waligned-new=all
    -Wall
    -Walloc-size
    -Walloc-zero
    -Walloca
    -Wanalyzer-allocation-size
    -Wanalyzer-deref-before-check
    -Wanalyzer-double-fclose
    -Wanalyzer-double-free
    -Wanalyzer-exposure-through-output-file
    -Wanalyzer-exposure-through-uninit-copy
    -Wanalyzer-fd-access-mode-mismatch
    -Wanalyzer-fd-double-close
    -Wanalyzer-fd-leak
    -Wanalyzer-fd-phase-mismatch
    -Wanalyzer-fd-type-mismatch
    -Wanalyzer-fd-use-after-close
    -Wanalyzer-fd-use-without-check
    -Wanalyzer-file-leak
    -Wanalyzer-free-of-non-heap
    -Wanalyzer-imprecise-fp-arithmetic
    -Wanalyzer-infinite-loop
    -Wanalyzer-infinite-recursion
    -Wanalyzer-jump-through-null
    -Wanalyzer-malloc-leak
    -Wanalyzer-mismatching-deallocation
    -Wanalyzer-null-argument
    -Wanalyzer-null-dereference
    -Wanalyzer-out-of-bounds
    -Wanalyzer-overlapping-buffers
    -Wanalyzer-possible-null-argument
    -Wanalyzer-possible-null-dereference
    -Wanalyzer-putenv-of-auto-var
    -Wanalyzer-shift-count-negative
    -Wanalyzer-shift-count-overflow
    -Wanalyzer-stale-setjmp-buffer
    -Wanalyzer-symbol-too-complex
    -Wanalyzer-tainted-allocation-size
    -Wanalyzer-tainted-array-index
    -Wanalyzer-tainted-assertion
    -Wanalyzer-tainted-divisor
    -Wanalyzer-tainted-offset
    -Wanalyzer-tainted-size
    -Wanalyzer-too-complex
    -Wanalyzer-undefined-behavior-ptrdiff
    -Wanalyzer-undefined-behavior-strtok
    -Wanalyzer-unsafe-call-within-signal-handler
    -Wanalyzer-use-after-free
    -Wanalyzer-use-of-pointer-in-stale-stack-frame
    -Wanalyzer-use-of-uninitialized-value
    -Wanalyzer-va-arg-type-mismatch
    -Wanalyzer-va-list-exhausted
    -Wanalyzer-va-list-leak
    -Wanalyzer-va-list-use-after-va-end
    -Wanalyzer-write-to-const
    -Wanalyzer-write-to-string-literal
    -Warith-conversion
    -Warray-bounds=2
    -Warray-compare
    -Warray-parameter=2
    -Wattribute-alias=2
    -Wattribute-warning
    -Wattributes
    -Wbool-compare
    -Wbool-operation
    -Wbuiltin-declaration-mismatch
    -Wbuiltin-macro-redefined
    -Wc++0x-compat
    -Wc++11-compat
    -Wc++11-extensions
    -Wc++14-compat
    -Wc++14-extensions
    -Wc++17-compat
    -Wc++17-extensions
    -Wc++1z-compat
    -Wc++20-compat
    -Wc++20-extensions
    -Wc++23-extensions
    -Wc++26-extensions
    -Wc++2a-compat
    -Wcalloc-transposed-args
    -Wcannot-profile
    -Wcast-align
    -Wcast-align=strict
    -Wcast-function-type
    -Wcast-qual
    -Wcast-user-defined
    -Wcatch-value=3
    -Wchanges-meaning
    -Wchar-subscripts
    -Wclass-conversion
    -Wclass-memaccess
    -Wclobbered
    -Wcomma-subscript
    -Wcomment
    -Wcomments
    -Wcomplain-wrong-lang
    -Wconditionally-supported
    -Wconversion
    -Wconversion-null
    -Wcoverage-invalid-line-number
    -Wcoverage-mismatch
    -Wcoverage-too-many-conditions
    -Wcoverage-too-many-paths
    -Wcpp
    -Wctad-maybe-unsupported
    -Wctor-dtor-privacy
    -Wdangling-else
    -Wdangling-pointer=2
    -Wdangling-reference
    -Wdate-time
    -Wdefaulted-function-deleted
    -Wdelete-incomplete
    -Wdelete-non-virtual-dtor
    -Wdeprecated
    -Wdeprecated-copy
    -Wdeprecated-copy-dtor
    -Wdeprecated-declarations
    -Wdeprecated-enum-enum-conversion
    -Wdeprecated-enum-float-conversion
    -Wdeprecated-literal-operator
    -Wdeprecated-variadic-comma-omission
    -Wdisabled-optimization
    -Wdiv-by-zero
    -Wdouble-promotion
    -Wduplicated-branches
    -Wduplicated-cond
    -Weffc++
    -Welaborated-enum-base
    -Wempty-body
    -Wendif-labels
    -Wenum-compare
    -Wenum-conversion
    -Wexceptions
    -Wexpansion-to-defined
    -Wextra
    -Wextra-semi
    -Wflex-array-member-not-at-end
    -Wfloat-conversion
    -Wfloat-equal
    -Wformat -Wformat-contains-nul
    -Wformat -Wformat-diag
    -Wformat -Wformat-extra-args
    -Wformat -Wformat-nonliteral
    -Wformat -Wformat-overflow=2
    -Wformat -Wformat-security
    -Wformat -Wformat-signedness
    -Wformat -Wformat-truncation=2
    -Wformat -Wformat-y2k
    -Wformat -Wformat-zero-length
    -Wformat=2
    -Wframe-address
    -Wfree-nonheap-object
    -Wglobal-module
    -Whardened
    -Wheader-guard
    -Whsa
    -Wif-not-aligned
    -Wignored-attributes
    -Wignored-qualifiers
    -Wimplicit-fallthrough=5
    -Winaccessible-base
    -Winfinite-recursion
    -Winherited-variadic-ctor
    -Winit-list-lifetime
    -Winit-self
    -Winline
    -Wint-in-bool-context
    -Wint-to-pointer-cast
    -Winterference-size
    -Winvalid-constexpr
    -Winvalid-imported-macros
    -Winvalid-memory-model
    -Winvalid-offsetof
    -Winvalid-pch
    -Winvalid-utf8
    -Wliteral-suffix
    -Wlogical-not-parentheses
    -Wlogical-op
    -Wno-long-long
    -Wlto-type-mismatch
    -Wmain
    -Wmaybe-musttail-local-addr
    -Wmaybe-uninitialized
    -Wmemset-elt-size
    -Wmemset-transposed-args
    -Wmisleading-indentation
    -Wmismatched-dealloc
    -Wmismatched-new-delete
    -Wmismatched-tags
    -Wmissing-attributes
    -Wmissing-braces
    -Wmissing-declarations
    -Wmissing-field-initializers
    -Wmissing-include-dirs
    -Wmissing-profile
    -Wmissing-requires
    -Wmissing-template-keyword
    -Wmultichar
    -Wmultiple-inheritance
    -Wmultistatement-macros
    -Wmusttail-local-addr
    -Wno-namespaces
    -Wnarrowing
    -Wnoexcept
    -Wnoexcept-type
    -Wnon-template-friend
    -Wnon-virtual-dtor
    -Wnonnull
    -Wnonnull-compare
    -Wnormalized=nfkc
    -Wno-nrvo
    -Wnull-dereference
    -Wodr
    -Wold-style-cast
    -Wopenacc-parallelism
    -Wopenmp
    -Wopenmp-simd
    -Woverflow
    -Woverlength-strings
    -Woverloaded-virtual=2
    -Wpacked
    -Wpacked-bitfield-compat
    -Wpacked-not-aligned
    -Wno-padded
    -Wparentheses
    -Wpedantic
    -Wpessimizing-move
    -Wplacement-new=2
    -Wpmf-conversions
    -Wpointer-arith
    -Wpointer-compare
    -Wpragma-once-outside-header
    -Wpragmas
    -Wprio-ctor-dtor
    -Wpsabi
    -Wrange-loop-construct
    -Wredundant-decls
    -Wredundant-move
    -Wredundant-tags
    -Wregister
    -Wreorder
    -Wrestrict
    -Wreturn-local-addr
    -Wreturn-type
    -Wscalar-storage-order
    -Wself-move
    -Wsequence-point
    -Wshadow=compatible-local
    -Wshadow=global
    -Wshadow=local
    -Wshift-count-negative
    -Wshift-count-overflow
    -Wshift-negative-value
    -Wshift-overflow=2
    -Wsign-compare
    -Wsign-conversion
    -Wsign-promo
    -Wsized-deallocation
    -Wsizeof-array-argument
    -Wsizeof-array-div
    -Wsizeof-pointer-div
    -Wsizeof-pointer-memaccess
    -Wstack-protector
    -Wstrict-aliasing
    -Wstrict-aliasing=3
    -Wstrict-null-sentinel
    -Wstrict-overflow
    -Wstring-compare
    -Wstringop-overflow
    -Wstringop-overflow=4
    -Wstringop-overread
    -Wstringop-truncation
    -Wsubobject-linkage
    -Wsuggest-attribute=cold
    -Wsuggest-attribute=const
    -Wsuggest-attribute=format
    -Wsuggest-attribute=malloc
    -Wsuggest-attribute=noreturn
    -Wsuggest-attribute=pure
    -Wsuggest-attribute=returns_nonnull
    -Wsuggest-final-methods
    -Wsuggest-final-types
    -Wsuggest-override
    -Wswitch
    -Wswitch-bool
    -Wswitch-default
    -Wswitch-enum
    -Wswitch-outside-range
    -Wswitch-unreachable
    -Wsync-nand
    -Wsynth
    -Wno-system-headers
    -Wtautological-compare
    -Wtemplate-body
    -Wtemplate-id-cdtor
    -Wtemplate-names-tu-local
    -Wno-templates
    -Wterminate
    -Wtrailing-whitespace
    -Wtrampolines
    -Wtrigraphs
    -Wtrivial-auto-var-init
    -Wtsan
    -Wtype-limits
    -Wundef
    -Wunicode
    -Wuninitialized
    -Wunknown-pragmas
    -Wunreachable-code
    -Wunsafe-loop-optimizations
    -Wunused
    -Wunused-but-set-parameter
    -Wunused-but-set-variable
    -Wunused-const-variable=2
    -Wunused-function
    -Wunused-label
    -Wunused-local-typedefs
    -Wunused-macros
    -Wunused-parameter
    -Wunused-result
    -Wunused-value
    -Wunused-variable
    -Wuse-after-free=3
    -Wuseless-cast
    -Wvarargs
    -Wvariadic-macros
    -Wvector-operation-performance
    -Wvexing-parse
    -Wvirtual-inheritance
    -Wvirtual-move-assign
    -Wvla
    -Wvla-parameter
    -Wvolatile
    -Wvolatile-register-var
    -Wwrite-strings
    -Wxor-used-as-pow
    -Wzero-as-null-pointer-constant
    -Wzero-length-bounds
)
