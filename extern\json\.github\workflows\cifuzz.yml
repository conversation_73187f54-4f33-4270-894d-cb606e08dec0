name: CIFuzz
on: [pull_request]

permissions:
  contents: read

jobs:
  Fuzzing:
    runs-on: ubuntu-latest
    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
      with:
        egress-policy: audit

    - name: Build Fuzzers
      id: build
      uses: google/oss-fuzz/infra/cifuzz/actions/build_fuzzers@57fe4475324c5506adbfecdcdd2917f65c86ee9e # master
      with:
        oss-fuzz-project-name: 'json'
        dry-run: false
        language: c++
    - name: Run Fuzzers
      uses: google/oss-fuzz/infra/cifuzz/actions/run_fuzzers@57fe4475324c5506adbfecdcdd2917f65c86ee9e # master
      with:
        oss-fuzz-project-name: 'json'
        fuzz-seconds: 300
        dry-run: false
        language: c++
    - name: Upload Crash
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
      if: failure() && steps.build.outcome == 'success'
      with:
        name: artifacts
        path: ./out/artifacts
