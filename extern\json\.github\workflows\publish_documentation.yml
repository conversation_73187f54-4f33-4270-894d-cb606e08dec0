name: Publish documentation

# publish the documentation on every merge to develop branch
on:
  push:
    branches:
      - develop
    paths:
      - docs/mkdocs/**
      - docs/examples/**
  workflow_dispatch:

# we don't want to have concurrent jobs, and we don't want to cancel running jobs to avoid broken publications
concurrency:
  group: documentation
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  publish_documentation:
    permissions:
      contents: write

    if: github.repository == 'nlohmann/json'
    runs-on: ubuntu-22.04
    steps:
      - name: Harden Runner
        uses: step-security/harden-runner@ec9f2d5744a09debf3a187a3f4f675c53b671911 # v2.13.0
        with:
          egress-policy: audit

      - uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0

      - name: Install virtual environment
        run: make install_venv -C docs/mkdocs

      - name: Build documentation
        run: make build -C docs/mkdocs

      - name: Deploy documentation
        uses: peaceiris/actions-gh-pages@4f9cc6602d3f66b9c108549d475ec49e8ef4d45e # v4.0.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./docs/mkdocs/site
