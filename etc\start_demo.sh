#!/bin/bash

echo "启动 MROS Demo 工程..."

# 编译项目
echo "编译项目..."
mkdir -p build
cd build
cmake ..
make -j4

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "编译成功！"

# 启动各个组件
echo "启动 SensorServer..."
./bin/mros_sensor &
SENSOR_PID=$!

echo "启动 Server..."
./bin/mros_server &
SERVER_PID=$!

echo "启动 Client..."
./bin/mros_client &
CLIENT_PID=$!

echo "所有组件已启动！"
echo "SensorServer PID: $SENSOR_PID"
echo "Server PID: $SERVER_PID"
echo "Client PID: $CLIENT_PID"

echo ""
echo "按 Ctrl+C 停止所有组件..."

# 等待用户中断
trap 'echo "正在停止所有组件..."; kill $SENSOR_PID $SERVER_PID $CLIENT_PID 2>/dev/null; exit 0' INT

# 保持脚本运行
while true; do
    sleep 1
done 