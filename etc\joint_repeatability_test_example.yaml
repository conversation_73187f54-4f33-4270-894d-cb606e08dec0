# 关节重复定位精度测试配置文件
# 文件名: joint_repeatability_test_example.yaml

# 测试基本参数
test_type: "joint_repeatability_test"
test_name: "关节重复定位精度测试"
tabName: "关节重复定位精度"

# 测试参数配置
target_angle_degrees: 90.0        # 目标角度（度）
target_speed: 10.0                # 目标速度（rad/s）
load_current: 30.0                # 负载电流（A）
target_torque: 50.0               # 目标扭矩（Nm）
current_gradient: 10.0            # 电流梯度（%/s）
stability_tolerance: 0.1          # 稳定性容差
stability_duration_ms: 2000       # 稳定性持续时间（毫秒）
test_cycles: 5                    # 测试循环次数

# 测试说明
description: |
  关节重复定位精度测试用于评估电机在负载条件下的位置控制精度和重复性。
  
  测试步骤：
  1. 负载线性加载：通过MODBUS-RTU协议控制磁粉制动器，采用10%/s电流梯度递增
  2. 记录电机起始位置：获取编码器计数值
  3. 执行定位控制：发送目标位置和速度指令，角度值转换为弧度值并放大10000倍
  4. 记录位置传感器数值：保持2s稳定时间，记录目标位置和实际位置
  5. 返回起始位置：控制电机返回初始位置，再次记录数据
  
  测试结果包括：
  - 最大位置误差
  - 平均位置误差
  - 位置误差标准差
  - 重复定位精度
  - 各测试循环的详细数据

# 测试要求
requirements:
  - "位置误差标准差应小于平均误差的20%"
  - "最大误差应小于平均误差的3倍"
  - "测试循环次数不少于5次"
  - "每次循环保持2s稳定时间"

# 预期结果
expected_results:
  repeatability_accuracy: "> 0.8"    # 重复定位精度应大于80%
  max_position_error: "< 1.0"         # 最大位置误差应小于1个编码器计数
  avg_position_error: "< 0.5"         # 平均位置误差应小于0.5个编码器计数
